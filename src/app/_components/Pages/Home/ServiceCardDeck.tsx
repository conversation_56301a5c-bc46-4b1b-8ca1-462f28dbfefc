'use client';

import { Service } from '@/src/app/_interfaces/service-type';
import Image from 'next/image';
import { useMemo } from 'react';

interface ServiceCardDeckProps {
  services?: Service[];
  className?: string;
}

interface CardConfig {
  x: number;
  y: number;
  rotation: number;
  scale: number;
  zIndex: number;
  shadowIntensity: number;
  width: number;
  height: number;
  type: 'service' | 'decorative';
  background?: string;
  borderRadius?: string;
}

export const ServiceCardDeck = ({ services = [], className = '' }: ServiceCardDeckProps) => {
  // Use useMemo to avoid recalculating on every render
  const displayServices = useMemo(() => {
    return services
      .filter((service) => service.imageUrl && service.imageUrl.trim() !== '')
      .slice(0, 3); // Only take first 3 services for the top layer
  }, [services]);

  // Card stack configuration for exactly 6 cards
  // First 3 cards (indices 0-2): Service images (top layer)
  // Last 3 cards (indices 3-5): Decorative gradient cards (bottom layer)
  const cardStackConfig: CardConfig[] = [
    // Service image cards (top layer) - indices 0-2
    {
      x: 0,
      y: 0,
      rotation: -6.378,
      scale: 1,
      zIndex: 5,
      shadowIntensity: 0.15,
      width: 213,
      height: 330,
      type: 'service',
    },
    {
      x: 140,
      y: -5,
      rotation: 1.454,
      scale: 0.92,
      zIndex: 4,
      shadowIntensity: 0.25,
      width: 137,
      height: 304,
      type: 'service',
    },
    {
      x: 220,
      y: -4,
      rotation: 12.588,
      scale: 0.85,
      zIndex: 3,
      shadowIntensity: 0.3,
      width: 104,
      height: 274,
      type: 'service',
    },
    // Decorative gradient cards (bottom layer) - indices 3-5
    {
      x: 300,
      y: 16,
      rotation: 15.845,
      scale: 0.78,
      zIndex: 0,
      shadowIntensity: 0.4,
      width: 44,
      height: 250,
      type: 'decorative',
      background: 'linear-gradient(9deg, #EDA909 4.35%, #FDE63E 143.99%)',
      borderRadius: '0px 24px 24px 0px',
    },
    {
      x: 320,
      y: 50,
      rotation: 22.36,
      scale: 0.96,
      zIndex: -1,
      shadowIntensity: 0.35,
      width: 34,
      height: 198,
      type: 'decorative',
      background: 'linear-gradient(4deg, #EDA909 65.57%, #FDE63E 130.13%)',
      borderRadius: '0px 24px 24px 0px',
    },
    {
      x: 334,
      y: 56,
      rotation: 25,
      scale: 0.88,
      zIndex: -2,
      shadowIntensity: 0.35,
      width: 34,
      height: 198,
      type: 'decorative',
      background: '#000000',
      borderRadius: '0px 16px 16px 0px',
    },
  ];

  return (
    <div className={`relative ${className}`}>
      <div className="relative h-[400px] w-[300px] max-w-full">
        {/* Render exactly 6 cards: 3 service cards + 3 decorative cards */}
        {cardStackConfig.map((config, index) => {
          const isServiceCard = config.type === 'service';
          const service = isServiceCard ? displayServices[index] : null;

          // Skip service cards if no service is available
          if (isServiceCard && !service) return null;

          return (
            <div
              key={isServiceCard ? service?.id : `decorative-${index}`}
              className={`absolute overflow-hidden transition-all duration-500 ease-out ${
                isServiceCard
                  ? 'group cursor-pointer hover:z-50 hover:rotate-0 hover:scale-110 hover:shadow-2xl'
                  : ''
              }`}
              style={{
                left: `${config.x}px`,
                top: `${config.y}px`,
                width: `${config.width}px`,
                height: `${config.height}px`,
                transform: `rotate(${config.rotation}deg) scale(${config.scale})`,
                zIndex: config.zIndex,
                borderRadius: config.borderRadius || '24px',
                boxShadow: `
                  0px ${8 + config.shadowIntensity * 20}px ${16 + config.shadowIntensity * 30}px -${4 + config.shadowIntensity * 8}px rgba(0, 0, 0, ${0.1 + config.shadowIntensity * 0.3}),
                  0px ${4 + config.shadowIntensity * 12}px ${8 + config.shadowIntensity * 20}px -${2 + config.shadowIntensity * 6}px rgba(0, 0, 0, ${0.05 + config.shadowIntensity * 0.2}),
                  0px ${2 + config.shadowIntensity * 8}px ${4 + config.shadowIntensity * 12}px -${1 + config.shadowIntensity * 4}px rgba(0, 0, 0, ${0.03 + config.shadowIntensity * 0.15})
                `,
                transformOrigin: 'center center',
                background: !isServiceCard ? config.background : undefined,
              }}
            >
              {isServiceCard && service ? (
                <>
                  {/* Service card with gradient border */}
                  <div
                    className="absolute inset-0"
                    style={{
                      background: 'linear-gradient(26deg, #EDA909 -40.7%, #FDE63E 95.2%)',
                      padding: '4px',
                      borderRadius: '24px',
                    }}
                  >
                    {/* Inner card content */}
                    <div className="relative h-full w-full overflow-hidden rounded-2xl bg-white">
                      {/* Service image */}
                      <div className="relative h-full w-full">
                        <Image
                          src={service.imageUrl}
                          alt={service.name}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                          sizes={`${config.width}px`}
                          quality={85}
                          priority={index < 3}
                        />
                        {/* Subtle overlay for depth */}
                        <div
                          className="absolute inset-0 bg-black transition-opacity duration-300 group-hover:opacity-0"
                          style={{
                            opacity: config.shadowIntensity * 0.2,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </>
              ) : null}
            </div>
          );
        })}
      </div>
    </div>
  );
};
