import { ServiceCardDeck } from '@/src/app/_components/Pages/Home/ServiceCardDeck';
import { ServiceCategory } from '@/src/app/_interfaces/service-type';

interface NewHeroProps {
  services?: ServiceCategory[];
}

export const NewHero = ({ services = [] }: NewHeroProps) => {
  // Flatten services from categories for the card deck
  const allServices = services.flatMap(
    (category) => category.subcategories?.flatMap((subcategory) => subcategory.services || []) || []
  );

  return (
    <div className="flex w-full flex-col items-center justify-between gap-10 text-black lg:flex-row">
      <div className="w-full md:w-9/12">
        <h1 className="text-5xl font-extrabold md:text-5xl lg:text-6xl">
          <strong className="bg-gradient-to-r from-amber-500 via-yellow-400 to-yellow-300 bg-clip-text text-transparent">
            Serviço garantido, <br />
          </strong>
          seguro e sem complicação.
        </h1>
        <p className="mt-6 text-lg font-medium md:text-xl lg:text-2xl">
          O GetNinjas ajuda você a contratar o serviço que você precisa com <br /> segurança e
          agilidade, em parceria com a Europ Assistance.
        </p>
      </div>

      {/* Service Card Deck */}
      <div className="mt-8 flex justify-center lg:mt-0 lg:w-3/12 lg:justify-end">
        <ServiceCardDeck
          services={allServices}
          className="flex-shrink-0 scale-75 md:scale-90 lg:scale-100"
        />
      </div>
    </div>
  );
};
